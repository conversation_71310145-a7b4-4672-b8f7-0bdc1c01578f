{"name": "bot-backend", "version": "1.0.0", "description": "Backend for bot application with code injection capabilities", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "init-db": "node src/utils/initDatabase.js"}, "keywords": ["bot", "discord", "telegram", "code-injection", "nodejs"], "author": "", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "isolated-vm": "^5.0.4", "jsonwebtoken": "^9.0.2", "sqlite3": "^5.1.6", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}