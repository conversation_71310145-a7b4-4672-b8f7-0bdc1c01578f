# Hướng dẫn sử dụng Bot Code Samples

## Cách nạp code vào bot

### 1. Tạo bot và lấy token
```bash
# Đăng ký user
curl -X POST http://***********:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "password123"}'

# Tạo bot
curl -X POST http://***********:3001/api/bots \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name": "My Test Bot", "description": "Bot for testing"}'
```

### 2. Nạp code vào bot
```bash
curl -X POST http://***********:3001/api/bots/BOT_ID/code \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "PASTE_CODE_HERE",
    "language": "javascript",
    "description": "Bot function description"
  }'
```

## Các mẫu code có sẵn

### 1. Echo Bot
**Mô tả**: Trả lại tin nhắn người dùng gửi

**Code để nạp**:
```javascript
const message = input.message || 'Hello!';
console.log('Received message:', message);
return 'Echo: ' + message;
```

**Cách test**:
```bash
curl -X POST http://***********:3001/api/bots/execute \
  -H "X-Bot-Token: YOUR_BOT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"command": "echo", "input_data": {"message": "Hello World!"}}'
```

### 2. Calculator Bot
**Mô tả**: Máy tính đơn giản

**Code để nạp**:
```javascript
const { operation, a, b } = input;
console.log('Calculator operation:', operation, a, b);

if (!operation || a === undefined || b === undefined) {
  return 'Usage: { "operation": "add|subtract|multiply|divide", "a": number, "b": number }';
}

switch(operation.toLowerCase()) {
  case 'add':
  case '+':
    return { result: a + b, operation: 'addition' };
  case 'subtract':
  case '-':
    return { result: a - b, operation: 'subtraction' };
  case 'multiply':
  case '*':
    return { result: a * b, operation: 'multiplication' };
  case 'divide':
  case '/':
    if (b === 0) return { error: 'Division by zero is not allowed' };
    return { result: a / b, operation: 'division' };
  default:
    return { error: 'Supported operations: add, subtract, multiply, divide' };
}
```

**Cách test**:
```bash
curl -X POST http://***********:3001/api/bots/execute \
  -H "X-Bot-Token: YOUR_BOT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"command": "calculate", "input_data": {"operation": "add", "a": 10, "b": 5}}'
```

### 3. Random Number Generator
**Mô tả**: Tạo số ngẫu nhiên

**Code để nạp**:
```javascript
const min = input.min || 1;
const max = input.max || 100;
const count = input.count || 1;

console.log('Generating random numbers:', min, 'to', max, 'count:', count);

if (min >= max) {
  return { error: 'Min must be less than max' };
}

if (count > 100) {
  return { error: 'Count cannot exceed 100' };
}

const numbers = [];
for (let i = 0; i < count; i++) {
  numbers.push(Math.floor(Math.random() * (max - min + 1)) + min);
}

return {
  numbers: numbers,
  min: min,
  max: max,
  count: count,
  sum: numbers.reduce((a, b) => a + b, 0),
  average: numbers.reduce((a, b) => a + b, 0) / numbers.length
};
```

**Cách test**:
```bash
curl -X POST http://***********:3001/api/bots/execute \
  -H "X-Bot-Token: YOUR_BOT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"command": "random", "input_data": {"min": 1, "max": 100, "count": 5}}'
```

### 4. Dice Roller
**Mô tả**: Tung xúc xắc

**Code để nạp**:
```javascript
const sides = input.sides || 6;
const count = input.count || 1;

console.log('Rolling dice:', count, 'x', sides, 'sided');

if (sides < 2 || sides > 100) {
  return { error: 'Dice sides must be between 2 and 100' };
}

if (count < 1 || count > 20) {
  return { error: 'Dice count must be between 1 and 20' };
}

const results = [];
for (let i = 0; i < count; i++) {
  results.push(Math.floor(Math.random() * sides) + 1);
}

return {
  dice: sides + '-sided',
  count: count,
  results: results,
  total: results.reduce((a, b) => a + b, 0),
  average: results.reduce((a, b) => a + b, 0) / results.length
};
```

**Cách test**:
```bash
curl -X POST http://***********:3001/api/bots/execute \
  -H "X-Bot-Token: YOUR_BOT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"command": "dice", "input_data": {"sides": 20, "count": 2}}'
```

### 5. Password Generator
**Mô tả**: Tạo mật khẩu ngẫu nhiên

**Code để nạp**:
```javascript
const length = input.length || 12;
const includeNumbers = input.includeNumbers !== false;
const includeSymbols = input.includeSymbols !== false;
const includeUppercase = input.includeUppercase !== false;
const includeLowercase = input.includeLowercase !== false;

console.log('Generating password with length:', length);

if (length < 4 || length > 50) {
  return { error: 'Password length must be between 4 and 50' };
}

let charset = '';
if (includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
if (includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
if (includeNumbers) charset += '0123456789';
if (includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';

if (charset === '') {
  return { error: 'At least one character type must be included' };
}

let password = '';
for (let i = 0; i < length; i++) {
  password += charset.charAt(Math.floor(Math.random() * charset.length));
}

// Calculate password strength
let strength = 0;
if (password.length >= 8) strength++;
if (/[a-z]/.test(password)) strength++;
if (/[A-Z]/.test(password)) strength++;
if (/[0-9]/.test(password)) strength++;
if (/[^a-zA-Z0-9]/.test(password)) strength++;

const strengthLevels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];

return {
  password: password,
  length: password.length,
  strength: strengthLevels[strength] || 'Very Weak',
  settings: {
    includeNumbers,
    includeSymbols,
    includeUppercase,
    includeLowercase
  }
};
```

**Cách test**:
```bash
curl -X POST http://***********:3001/api/bots/execute \
  -H "X-Bot-Token: YOUR_BOT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"command": "password", "input_data": {"length": 16, "includeSymbols": true}}'
```

## Lưu ý quan trọng

1. **Input Data**: Tất cả dữ liệu đầu vào được truyền qua object `input`
2. **Return Value**: Bot sẽ trả về giá trị cuối cùng của code
3. **Console.log**: Sử dụng `console.log()` để debug, logs sẽ hiển thị trong response
4. **Error Handling**: Luôn kiểm tra input và trả về error message rõ ràng
5. **Security**: Code chạy trong sandbox an toàn, không thể truy cập file system

## Tạo code tùy chỉnh

Bạn có thể tạo code riêng theo template:

```javascript
// Lấy input data
const { param1, param2 } = input;

// Validate input
if (!param1) {
  return { error: 'param1 is required' };
}

// Logic xử lý
console.log('Processing:', param1, param2);

// Trả về kết quả
return {
  result: 'your result',
  input: { param1, param2 },
  timestamp: new Date().toISOString()
};
```
