/**
 * Simple Bot Client - <PERSON><PERSON> dụ đơn giản sử dụng bot token
 * 
 * <PERSON><PERSON>ch sử dụng:
 * 1. Lấy bot token từ API
 * 2. Thay YOUR_BOT_TOKEN bằng token thật
 * 3. Chạy: node examples/simple-bot-client.js
 */

const axios = require('axios');

// C<PERSON>u hình
const BOT_TOKEN = 'YOUR_BOT_TOKEN_HERE'; // Thay bằng token thật của bạn
const API_BASE = 'http://192.168.2.4:3001/api';

// Tạo axios client với bot token
const botAPI = axios.create({
  baseURL: API_BASE,
  headers: {
    'X-Bot-Token': BOT_TOKEN,
    'Content-Type': 'application/json'
  }
});

// Hàm thực thi lệnh bot
async function executeBot(command, inputData = {}) {
  try {
    const response = await botAPI.post('/bots/execute', {
      command: command,
      input_data: inputData
    });
    
    console.log(`✅ Command '${command}' executed successfully:`);
    console.log('Result:', response.data.result);
    console.log('Execution time:', response.data.execution_time_ms + 'ms');
    
    return response.data;
  } catch (error) {
    console.error(`❌ Command '${command}' failed:`, error.response?.data || error.message);
    return null;
  }
}

// Hàm lấy thông tin bot
async function getBotInfo() {
  try {
    const response = await botAPI.get('/bots/info');
    console.log('🤖 Bot Info:', response.data.bot);
    console.log('📊 Stats:', response.data.stats);
    return response.data;
  } catch (error) {
    console.error('❌ Failed to get bot info:', error.response?.data || error.message);
    return null;
  }
}

// Main function - Ví dụ sử dụng bot
async function main() {
  console.log('🚀 Starting Simple Bot Client\n');

  // Kiểm tra token
  if (BOT_TOKEN === 'YOUR_BOT_TOKEN_HERE') {
    console.log('❌ Please replace YOUR_BOT_TOKEN_HERE with your actual bot token');
    console.log('You can get bot token by creating a bot via API');
    return;
  }

  // 1. Lấy thông tin bot
  console.log('1. Getting bot info...');
  await getBotInfo();
  console.log('');

  // 2. Test Echo command
  console.log('2. Testing Echo command...');
  await executeBot('echo', { 
    message: 'Hello from Simple Bot Client!' 
  });
  console.log('');

  // 3. Test Calculator
  console.log('3. Testing Calculator...');
  await executeBot('calculate', { 
    operation: 'add', 
    a: 15, 
    b: 25 
  });
  console.log('');

  // 4. Test Random Number Generator
  console.log('4. Testing Random Number Generator...');
  await executeBot('random', { 
    min: 1, 
    max: 100, 
    count: 5 
  });
  console.log('');

  // 5. Test Dice Roller
  console.log('5. Testing Dice Roller...');
  await executeBot('dice', { 
    sides: 20, 
    count: 3 
  });
  console.log('');

  // 6. Test Password Generator
  console.log('6. Testing Password Generator...');
  await executeBot('password', { 
    length: 12, 
    includeSymbols: true 
  });
  console.log('');

  console.log('✨ All tests completed!');
}

// Chạy ví dụ
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { executeBot, getBotInfo };
