/**
 * <PERSON><PERSON> dụ: Tạo bot giống Discord Bot
 * Sử dụng Bot Backend API
 */

const axios = require('axios');

const API_BASE = 'http://***********:3001/api';

class DiscordLikeBot {
  constructor(botToken) {
    this.botToken = botToken;
    this.commands = new Map();

    this.apiClient = axios.create({
      baseURL: API_BASE,
      headers: {
        'X-Bot-Token': botToken,
        'Content-Type': 'application/json'
      }
    });
  }

  // Đăng ký command (giống Discord.js)
  addCommand(name, code, description) {
    this.commands.set(name, { code, description });
  }

  // Upload tất cả commands lên server
  async deployCommands(userToken, botId) {
    console.log('Deploying commands to bot...');

    for (const [name, command] of this.commands) {
      try {
        await axios.post(`${API_BASE}/bots/${botId}/code`, {
          code: command.code,
          language: 'javascript',
          description: `${name}: ${command.description}`
        }, {
          headers: { 'Authorization': `Bearer ${userToken}` }
        });

        console.log(`✓ Command '${name}' deployed`);
      } catch (error) {
        console.error(`✗ Failed to deploy command '${name}':`, error.response?.data || error.message);
      }
    }
  }

  // Thực thi command
  async executeCommand(commandName, args = {}) {
    try {
      const response = await this.apiClient.post('/bots/execute', {
        command: commandName,
        input_data: args
      });
      return response.data;
    } catch (error) {
      console.error(`Command '${commandName}' failed:`, error.response?.data || error.message);
      return { success: false, error: error.message };
    }
  }
}

// Tạo bot với các commands giống Discord
async function createDiscordLikeBot() {
  console.log('=== CREATING DISCORD-LIKE BOT ===\n');

  // 1. Đăng ký user
  const userResponse = await axios.post(`${API_BASE}/auth/register`, {
    username: 'discord_dev',
    password: 'password123',
    email: '<EMAIL>'
  });

  const userToken = userResponse.data.token;
  console.log('✓ Developer registered');

  // 2. Tạo bot
  const botResponse = await axios.post(`${API_BASE}/bots`, {
    name: 'Discord-Like Bot',
    description: 'Bot with Discord-like commands'
  }, {
    headers: { 'Authorization': `Bearer ${userToken}` }
  });

  const bot = botResponse.data.bot;
  const botToken = bot.token;
  console.log(`✓ Bot created with token: ${botToken}`);

  // 3. Tạo bot client và thêm commands
  const discordBot = new DiscordLikeBot(botToken);

  // Command: ping
  discordBot.addCommand('ping', `
    console.log('Ping command executed');
    return 'Pong! 🏓';
  `, 'Responds with Pong!');

  // Command: userinfo
  discordBot.addCommand('userinfo', `
    const { username, id } = input;
    console.log('Getting user info for:', username);

    return {
      username: username || 'Unknown',
      id: id || 'N/A',
      joinedAt: new Date().toISOString(),
      status: 'online'
    };
  `, 'Get user information');

  // Command: roll dice
  discordBot.addCommand('roll', `
    const sides = input.sides || 6;
    const count = input.count || 1;

    console.log('Rolling dice:', count, 'x', sides);

    const results = [];
    for (let i = 0; i < count; i++) {
      results.push(Math.floor(Math.random() * sides) + 1);
    }

    return {
      dice: sides + '-sided',
      count: count,
      results: results,
      total: results.reduce((a, b) => a + b, 0)
    };
  `, 'Roll dice');

  // Command: weather (mock)
  discordBot.addCommand('weather', `
    const city = input.city || 'Unknown';
    console.log('Getting weather for:', city);

    const conditions = ['sunny', 'cloudy', 'rainy', 'snowy'];
    const temps = [15, 20, 25, 30, 35];

    return {
      city: city,
      condition: conditions[Math.floor(Math.random() * conditions.length)],
      temperature: temps[Math.floor(Math.random() * temps.length)] + '°C',
      humidity: Math.floor(Math.random() * 100) + '%'
    };
  `, 'Get weather information');

  // Command: help
  discordBot.addCommand('help', `
    const commands = [
      { name: 'ping', description: 'Responds with Pong!' },
      { name: 'userinfo', description: 'Get user information' },
      { name: 'roll', description: 'Roll dice (sides, count)' },
      { name: 'weather', description: 'Get weather info (city)' },
      { name: 'help', description: 'Show this help message' }
    ];

    return {
      title: 'Available Commands',
      commands: commands
    };
  `, 'Show help message');

  // 4. Deploy commands
  await discordBot.deployCommands(userToken, bot.id);

  // 5. Test commands
  console.log('\n=== TESTING COMMANDS ===');

  // Test ping
  console.log('\n--- !ping ---');
  const pingResult = await discordBot.executeCommand('ping');
  console.log('Result:', pingResult);

  // Test userinfo
  console.log('\n--- !userinfo ---');
  const userinfoResult = await discordBot.executeCommand('userinfo', {
    username: 'TestUser',
    id: '123456789'
  });
  console.log('Result:', userinfoResult);

  // Test roll
  console.log('\n--- !roll ---');
  const rollResult = await discordBot.executeCommand('roll', {
    sides: 20,
    count: 2
  });
  console.log('Result:', rollResult);

  // Test weather
  console.log('\n--- !weather ---');
  const weatherResult = await discordBot.executeCommand('weather', {
    city: 'Ho Chi Minh City'
  });
  console.log('Result:', weatherResult);

  // Test help
  console.log('\n--- !help ---');
  const helpResult = await discordBot.executeCommand('help');
  console.log('Result:', helpResult);

  console.log('\n=== BOT IS READY! ===');
  console.log(`Bot Token: ${botToken}`);
  console.log('You can now use this token to interact with the bot from external applications!');

  return { botToken, bot };
}

// Chạy nếu file được gọi trực tiếp
if (require.main === module) {
  createDiscordLikeBot().catch(console.error);
}

module.exports = { DiscordLikeBot, createDiscordLikeBot };
