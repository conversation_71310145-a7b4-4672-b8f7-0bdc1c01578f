# Bot Backend API

Một backend Node.js cho ứng dụng bot với khả năng nạp code qua token như Discord/Telegram bot.

## Tính năng

- 🤖 **Quản lý Bot**: Tạo, cập nhật, xóa bot với token riêng
- 💉 **Code Injection**: Nạp và thực thi JavaScript code động
- 🔐 **<PERSON><PERSON><PERSON> thực**: JWT cho user, token riêng cho bot
- 🗄️ **SQLite3 Database**: Lưu trữ dữ liệu local
- 🛡️ **Bảo mật**: Rate limiting, code validation, sandbox execution
- 📊 **Logging**: Theo dõi execution logs và thống kê

## Cài đặt

1. **Clone và cài đặt dependencies:**
```bash
npm install
```

2. **Tạo file environment:**
```bash
cp .env.example .env
```

3. **Khởi tạo database:**
```bash
npm run init-db
```

4. **Chạy server:**
```bash
# Development
npm run dev

# Production
npm start
```

## API Endpoints

### Authentication

- `POST /api/auth/register` - Đăng ký user mới
- `POST /api/auth/login` - Đăng nhập
- `GET /api/auth/me` - Thông tin user hiện tại

### Bot Management

- `POST /api/bots` - Tạo bot mới
- `GET /api/bots` - Lấy danh sách bot
- `GET /api/bots/:botId` - Thông tin bot cụ thể
- `PUT /api/bots/:botId` - Cập nhật bot
- `DELETE /api/bots/:botId` - Xóa bot

### Code Injection

- `GET /api/bots/:botId/code` - Lấy code injections
- `POST /api/bots/:botId/code` - Thêm code injection
- `POST /api/bots/:botId/code/:injectionId/execute` - Thực thi code

### Bot API (External)

- `POST /api/bots/execute` - Thực thi lệnh bot (cần bot token)
- `GET /api/bots/info` - Thông tin bot (cần bot token)

## Cách sử dụng

### 1. Đăng ký và tạo bot

```bash
# Đăng ký user
curl -X POST http://***********:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "password123"}'

# Tạo bot
curl -X POST http://***********:3000/api/bots \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name": "My Bot", "description": "Test bot"}'
```

### 2. Thêm code injection

```bash
curl -X POST http://***********:3000/api/bots/BOT_ID/code \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "console.log(\"Hello from bot!\"); return input.message;",
    "language": "javascript",
    "description": "Echo bot"
  }'
```

### 3. Sử dụng bot từ external client

```bash
curl -X POST http://***********:3000/api/bots/execute \
  -H "X-Bot-Token: YOUR_BOT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "command": "echo",
    "input_data": {"message": "Hello World!"}
  }'
```

## Workflow giống Discord/Telegram Bot

### 1. Developer tạo bot và nhận token
```javascript
// Tạo bot
const bot = await createBot({
  name: "My Discord Bot",
  description: "Bot tự động trả lời"
});

// Nhận token để sử dụng
const botToken = bot.token; // Ví dụ: "a1b2c3d4e5f6..."
```

### 2. Developer nạp code vào bot (giống Discord.js)
```javascript
// Code cho command !ping
const pingCommand = `
  console.log('Ping command executed');
  return 'Pong! 🏓';
`;

// Code cho command !weather
const weatherCommand = `
  const city = input.city || 'Ho Chi Minh City';
  // Logic lấy thời tiết...
  return {
    city: city,
    temperature: '28°C',
    condition: 'sunny'
  };
`;

// Upload code lên bot
await uploadBotCode(botToken, pingCommand, 'ping');
await uploadBotCode(botToken, weatherCommand, 'weather');
```

### 3. External app/user sử dụng bot
```javascript
// Từ ứng dụng chat, game, website...
const botClient = new BotClient(botToken);

// Thực thi lệnh
const response = await botClient.execute('ping');
// → "Pong! 🏓"

const weather = await botClient.execute('weather', { city: 'Da Nang' });
// → { city: 'Da Nang', temperature: '28°C', condition: 'sunny' }
```

## Database Schema

### Bảng `bots`
- `id` - Primary key
- `name` - Tên bot
- `token` - Token xác thực bot
- `description` - Mô tả
- `owner_id` - ID của user sở hữu
- `is_active` - Trạng thái hoạt động

### Bảng `code_injections`
- `id` - Primary key
- `bot_id` - ID bot
- `code` - Mã JavaScript
- `language` - Ngôn ngữ (mặc định: javascript)
- `description` - Mô tả
- `execution_count` - Số lần thực thi

### Bảng `execution_logs`
- `id` - Primary key
- `bot_id` - ID bot
- `input_data` - Dữ liệu đầu vào
- `output_data` - Kết quả
- `error_message` - Lỗi (nếu có)
- `execution_time_ms` - Thời gian thực thi

## Bảo mật

- **Code Sandbox**: Sử dụng VM2 để thực thi code an toàn
- **Rate Limiting**: Giới hạn số request
- **Input Validation**: Kiểm tra code trước khi thực thi
- **Token Authentication**: JWT cho user, token riêng cho bot
- **SQL Injection Protection**: Sử dụng prepared statements

## Ví dụ Code Injection

```javascript
// Echo bot
return input.message;

// Calculator bot
const { operation, a, b } = input;
switch(operation) {
  case 'add': return a + b;
  case 'subtract': return a - b;
  case 'multiply': return a * b;
  case 'divide': return b !== 0 ? a / b : 'Error: Division by zero';
  default: return 'Unknown operation';
}

// Random number generator
const min = input.min || 1;
const max = input.max || 100;
return Math.floor(Math.random() * (max - min + 1)) + min;
```

## Development

```bash
# Chạy tests
npm test

# Khởi tạo lại database
npm run init-db
```

## Environment Variables

- `PORT` - Port server (mặc định: 3000)
- `NODE_ENV` - Environment (development/production)
- `DB_PATH` - Đường dẫn database SQLite
- `JWT_SECRET` - Secret key cho JWT
- `MAX_CODE_SIZE` - Kích thước tối đa của code (bytes)
- `RATE_LIMIT_WINDOW_MS` - Thời gian window cho rate limiting
- `RATE_LIMIT_MAX_REQUESTS` - Số request tối đa trong window
