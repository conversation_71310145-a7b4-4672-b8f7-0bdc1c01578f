/**
 * <PERSON>ác mẫu code để nạp vào bot
 * Copy và paste vào bot của bạn
 */

// ===== 1. ECHO BOT =====
// Trả lại tin nhắn người dùng gửi
const echoBot = `
// Echo Bot - Trả lại tin nhắn
const message = input.message || 'Hello!';
console.log('Received message:', message);
return 'Echo: ' + message;
`;

// ===== 2. CALCULATOR BOT =====
// Máy tính đơn giản
const calculatorBot = `
// Calculator Bot
const { operation, a, b } = input;
console.log('Calculator operation:', operation, a, b);

if (!operation || a === undefined || b === undefined) {
  return 'Usage: { "operation": "add|subtract|multiply|divide", "a": number, "b": number }';
}

switch(operation.toLowerCase()) {
  case 'add':
  case '+':
    return { result: a + b, operation: 'addition' };
  case 'subtract':
  case '-':
    return { result: a - b, operation: 'subtraction' };
  case 'multiply':
  case '*':
    return { result: a * b, operation: 'multiplication' };
  case 'divide':
  case '/':
    if (b === 0) return { error: 'Division by zero is not allowed' };
    return { result: a / b, operation: 'division' };
  default:
    return { error: 'Supported operations: add, subtract, multiply, divide' };
}
`;

// ===== 3. RANDOM NUMBER GENERATOR =====
// Tạo số ngẫu nhiên
const randomBot = `
// Random Number Generator
const min = input.min || 1;
const max = input.max || 100;
const count = input.count || 1;

console.log('Generating random numbers:', min, 'to', max, 'count:', count);

if (min >= max) {
  return { error: 'Min must be less than max' };
}

if (count > 100) {
  return { error: 'Count cannot exceed 100' };
}

const numbers = [];
for (let i = 0; i < count; i++) {
  numbers.push(Math.floor(Math.random() * (max - min + 1)) + min);
}

return {
  numbers: numbers,
  min: min,
  max: max,
  count: count,
  sum: numbers.reduce((a, b) => a + b, 0),
  average: numbers.reduce((a, b) => a + b, 0) / numbers.length
};
`;

// ===== 4. DICE ROLLER =====
// Tung xúc xắc
const diceBot = `
// Dice Roller Bot
const sides = input.sides || 6;
const count = input.count || 1;

console.log('Rolling dice:', count, 'x', sides, 'sided');

if (sides < 2 || sides > 100) {
  return { error: 'Dice sides must be between 2 and 100' };
}

if (count < 1 || count > 20) {
  return { error: 'Dice count must be between 1 and 20' };
}

const results = [];
for (let i = 0; i < count; i++) {
  results.push(Math.floor(Math.random() * sides) + 1);
}

return {
  dice: sides + '-sided',
  count: count,
  results: results,
  total: results.reduce((a, b) => a + b, 0),
  average: results.reduce((a, b) => a + b, 0) / results.length
};
`;

// ===== 5. TEXT PROCESSOR =====
// Xử lý văn bản
const textBot = `
// Text Processor Bot
const text = input.text || '';
const action = input.action || 'info';

if (!text) {
  return { error: 'Please provide text to process' };
}

console.log('Processing text:', action, text.substring(0, 50) + '...');

switch(action.toLowerCase()) {
  case 'uppercase':
  case 'upper':
    return { result: text.toUpperCase(), action: 'uppercase' };
  
  case 'lowercase':
  case 'lower':
    return { result: text.toLowerCase(), action: 'lowercase' };
  
  case 'reverse':
    return { result: text.split('').reverse().join(''), action: 'reverse' };
  
  case 'wordcount':
  case 'count':
    const words = text.trim().split(/\\s+/).filter(word => word.length > 0);
    return {
      wordCount: words.length,
      characterCount: text.length,
      characterCountNoSpaces: text.replace(/\\s/g, '').length,
      action: 'count'
    };
  
  case 'info':
  default:
    const words = text.trim().split(/\\s+/).filter(word => word.length > 0);
    return {
      text: text,
      length: text.length,
      wordCount: words.length,
      firstWord: words[0] || '',
      lastWord: words[words.length - 1] || '',
      hasNumbers: /\\d/.test(text),
      hasSpecialChars: /[^a-zA-Z0-9\\s]/.test(text)
    };
}
`;

// ===== 6. WEATHER SIMULATOR =====
// Mô phỏng thời tiết
const weatherBot = `
// Weather Simulator Bot
const city = input.city || 'Unknown City';
const country = input.country || 'Vietnam';

console.log('Getting weather for:', city, country);

// Simulate weather data
const conditions = ['sunny', 'cloudy', 'rainy', 'partly cloudy', 'stormy', 'foggy'];
const temperatures = [18, 22, 25, 28, 30, 32, 35];
const humidity = Math.floor(Math.random() * 40) + 40; // 40-80%
const windSpeed = Math.floor(Math.random() * 20) + 5; // 5-25 km/h

const condition = conditions[Math.floor(Math.random() * conditions.length)];
const temperature = temperatures[Math.floor(Math.random() * temperatures.length)];

// Generate forecast for next 3 days
const forecast = [];
for (let i = 1; i <= 3; i++) {
  const date = new Date();
  date.setDate(date.getDate() + i);
  
  forecast.push({
    date: date.toISOString().split('T')[0],
    condition: conditions[Math.floor(Math.random() * conditions.length)],
    temperature: temperatures[Math.floor(Math.random() * temperatures.length)],
    humidity: Math.floor(Math.random() * 40) + 40
  });
}

return {
  location: {
    city: city,
    country: country
  },
  current: {
    condition: condition,
    temperature: temperature + '°C',
    humidity: humidity + '%',
    windSpeed: windSpeed + ' km/h',
    timestamp: new Date().toISOString()
  },
  forecast: forecast
};
`;

// ===== 7. QUIZ BOT =====
// Bot câu hỏi
const quizBot = `
// Quiz Bot
const action = input.action || 'question';
const answer = input.answer;

// Simple quiz questions
const questions = [
  { q: 'What is the capital of Vietnam?', a: 'hanoi', options: ['Ho Chi Minh City', 'Hanoi', 'Da Nang', 'Hue'] },
  { q: 'What is 2 + 2?', a: '4', options: ['3', '4', '5', '6'] },
  { q: 'Which programming language is this bot written in?', a: 'javascript', options: ['Python', 'JavaScript', 'Java', 'C++'] },
  { q: 'What year was JavaScript created?', a: '1995', options: ['1993', '1995', '1997', '1999'] },
  { q: 'What does API stand for?', a: 'application programming interface', options: ['App Program Interface', 'Application Programming Interface', 'Applied Program Integration', 'Advanced Programming Interface'] }
];

console.log('Quiz bot action:', action);

if (action === 'question') {
  const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
  return {
    question: randomQuestion.q,
    options: randomQuestion.options,
    instruction: 'Send answer with: { "action": "answer", "answer": "your answer" }'
  };
}

if (action === 'answer') {
  if (!answer) {
    return { error: 'Please provide an answer' };
  }
  
  // Find question that matches the answer (simple implementation)
  const correctQuestion = questions.find(q => 
    q.a.toLowerCase() === answer.toLowerCase() || 
    q.options.some(opt => opt.toLowerCase() === answer.toLowerCase())
  );
  
  if (correctQuestion && correctQuestion.a.toLowerCase() === answer.toLowerCase()) {
    return {
      result: 'Correct! 🎉',
      answer: answer,
      question: correctQuestion.q
    };
  } else {
    return {
      result: 'Incorrect! 😞',
      answer: answer,
      hint: 'Try again with a new question'
    };
  }
}

return { error: 'Unknown action. Use "question" or "answer"' };
`;

// ===== 8. PASSWORD GENERATOR =====
// Tạo mật khẩu
const passwordBot = `
// Password Generator Bot
const length = input.length || 12;
const includeNumbers = input.includeNumbers !== false;
const includeSymbols = input.includeSymbols !== false;
const includeUppercase = input.includeUppercase !== false;
const includeLowercase = input.includeLowercase !== false;

console.log('Generating password with length:', length);

if (length < 4 || length > 50) {
  return { error: 'Password length must be between 4 and 50' };
}

let charset = '';
if (includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
if (includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
if (includeNumbers) charset += '0123456789';
if (includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';

if (charset === '') {
  return { error: 'At least one character type must be included' };
}

let password = '';
for (let i = 0; i < length; i++) {
  password += charset.charAt(Math.floor(Math.random() * charset.length));
}

// Calculate password strength
let strength = 0;
if (password.length >= 8) strength++;
if (/[a-z]/.test(password)) strength++;
if (/[A-Z]/.test(password)) strength++;
if (/[0-9]/.test(password)) strength++;
if (/[^a-zA-Z0-9]/.test(password)) strength++;

const strengthLevels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];

return {
  password: password,
  length: password.length,
  strength: strengthLevels[strength] || 'Very Weak',
  settings: {
    includeNumbers,
    includeSymbols,
    includeUppercase,
    includeLowercase
  }
};
`;

module.exports = {
  echoBot,
  calculatorBot,
  randomBot,
  diceBot,
  textBot,
  weatherBot,
  quizBot,
  passwordBot
};
