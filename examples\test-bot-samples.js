/**
 * <PERSON>ript test các code mẫu bot
 * Chạy: node examples/test-bot-samples.js
 */

const axios = require('axios');
const { 
  echoBot, 
  calculatorBot, 
  randomBot, 
  diceBot, 
  passwordBot 
} = require('./bot-code-samples');

const API_BASE = 'http://***********:3001/api';

async function testBotSamples() {
  try {
    console.log('🤖 Testing Bot Code Samples\n');

    // 1. Đăng ký user
    console.log('1. Registering user...');
    const userResponse = await axios.post(`${API_BASE}/auth/register`, {
      username: 'sample_tester_' + Date.now(),
      password: 'password123',
      email: `tester${Date.now()}@example.com`
    });
    
    const userToken = userResponse.data.token;
    console.log('✓ User registered successfully\n');

    // 2. Tạo bot
    console.log('2. Creating bot...');
    const botResponse = await axios.post(`${API_BASE}/bots`, {
      name: 'Sample Test Bot',
      description: 'Bot for testing code samples'
    }, {
      headers: { 'Authorization': `Bearer ${userToken}` }
    });

    const bot = botResponse.data.bot;
    const botToken = bot.token;
    console.log(`✓ Bot created with token: ${botToken.substring(0, 10)}...\n`);

    // 3. Nạp các code mẫu
    console.log('3. Uploading code samples...');
    
    const samples = [
      { name: 'Echo Bot', code: echoBot, description: 'Echo messages back' },
      { name: 'Calculator', code: calculatorBot, description: 'Simple calculator' },
      { name: 'Random Generator', code: randomBot, description: 'Generate random numbers' },
      { name: 'Dice Roller', code: diceBot, description: 'Roll dice' },
      { name: 'Password Generator', code: passwordBot, description: 'Generate passwords' }
    ];

    for (const sample of samples) {
      await axios.post(`${API_BASE}/bots/${bot.id}/code`, {
        code: sample.code,
        language: 'javascript',
        description: sample.description
      }, {
        headers: { 'Authorization': `Bearer ${userToken}` }
      });
      console.log(`✓ Uploaded: ${sample.name}`);
    }

    console.log('\n4. Testing bot functions...\n');

    // Bot client để test
    const botClient = axios.create({
      baseURL: API_BASE,
      headers: {
        'X-Bot-Token': botToken,
        'Content-Type': 'application/json'
      }
    });

    // Test Echo Bot
    console.log('--- Testing Echo Bot ---');
    const echoResult = await botClient.post('/bots/execute', {
      command: 'echo',
      input_data: { message: 'Hello from test script!' }
    });
    console.log('Result:', echoResult.data.result);
    console.log('Success:', echoResult.data.success);

    // Test Calculator
    console.log('\n--- Testing Calculator ---');
    const calcResult = await botClient.post('/bots/execute', {
      command: 'calculate',
      input_data: { operation: 'multiply', a: 7, b: 8 }
    });
    console.log('Result:', calcResult.data.result);
    console.log('Success:', calcResult.data.success);

    // Test Random Generator
    console.log('\n--- Testing Random Generator ---');
    const randomResult = await botClient.post('/bots/execute', {
      command: 'random',
      input_data: { min: 1, max: 10, count: 3 }
    });
    console.log('Result:', randomResult.data.result);
    console.log('Success:', randomResult.data.success);

    // Test Dice Roller
    console.log('\n--- Testing Dice Roller ---');
    const diceResult = await botClient.post('/bots/execute', {
      command: 'dice',
      input_data: { sides: 20, count: 2 }
    });
    console.log('Result:', diceResult.data.result);
    console.log('Success:', diceResult.data.success);

    // Test Password Generator
    console.log('\n--- Testing Password Generator ---');
    const passwordResult = await botClient.post('/bots/execute', {
      command: 'password',
      input_data: { length: 12, includeSymbols: true }
    });
    console.log('Result:', passwordResult.data.result);
    console.log('Success:', passwordResult.data.success);

    console.log('\n🎉 All tests completed successfully!');
    console.log(`\n📋 Bot Information:`);
    console.log(`Bot ID: ${bot.id}`);
    console.log(`Bot Token: ${botToken}`);
    console.log(`API Base: ${API_BASE}`);
    
    console.log(`\n🔧 You can now use this bot with:`);
    console.log(`curl -X POST ${API_BASE}/bots/execute \\`);
    console.log(`  -H "X-Bot-Token: ${botToken}" \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -d '{"command": "echo", "input_data": {"message": "Hello!"}}'`);

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.status === 409) {
      console.log('\n💡 Tip: User might already exist. Try with a different username.');
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Tip: Make sure the server is running on http://***********:3001');
    }
  }
}

// Chạy test nếu file được gọi trực tiếp
if (require.main === module) {
  testBotSamples();
}

module.exports = { testBotSamples };
