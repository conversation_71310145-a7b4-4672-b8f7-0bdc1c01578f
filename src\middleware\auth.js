const jwt = require('jsonwebtoken');
const Bot = require('../models/Bot');

// Middleware để xác thực JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      console.error('JWT verification error:', err.message);
      return res.status(403).json({ error: 'Invalid or expired token' });
    }

    // Debug logging
    console.log('Authenticated user:', user);

    req.user = user;
    next();
  });
};

// Middleware để xác thực bot token
const authenticateBotToken = async (req, res, next) => {
  const botToken = req.headers['x-bot-token'] || req.query.token;

  if (!botToken) {
    return res.status(401).json({ error: 'Bot token required' });
  }

  try {
    const bot = await Bot.findByToken(botToken);
    if (!bot) {
      return res.status(401).json({ error: 'Invalid bot token' });
    }

    if (!bot.is_active) {
      return res.status(403).json({ error: 'Bot is inactive' });
    }

    req.bot = bot;
    next();
  } catch (error) {
    console.error('Bot authentication error:', error);
    res.status(500).json({ error: 'Authentication failed' });
  }
};

// Middleware để kiểm tra quyền sở hữu bot
const checkBotOwnership = async (req, res, next) => {
  try {
    const botId = req.params.botId || req.body.bot_id;
    if (!botId) {
      return res.status(400).json({ error: 'Bot ID required' });
    }

    const bot = await Bot.findById(botId);
    if (!bot) {
      return res.status(404).json({ error: 'Bot not found' });
    }

    // Debug logging
    console.log('Bot ownership check:');
    console.log('- User ID:', req.user.id, typeof req.user.id);
    console.log('- Bot owner ID:', bot.owner_id, typeof bot.owner_id);
    console.log('- Bot ID:', bot.id);

    // Convert both to string for comparison to handle type mismatch
    const userId = String(req.user.id);
    const ownerId = String(bot.owner_id);

    if (ownerId !== userId) {
      console.log('Access denied: User does not own this bot');
      return res.status(403).json({ error: 'Access denied: You do not own this bot' });
    }

    req.bot = bot;
    next();
  } catch (error) {
    console.error('Bot ownership check error:', error);
    res.status(500).json({ error: 'Authorization failed' });
  }
};

// Middleware để validate session token
const validateSessionToken = async (req, res, next) => {
  const sessionToken = req.headers['x-session-token'];

  if (!sessionToken) {
    return res.status(401).json({ error: 'Session token required' });
  }

  try {
    const database = require('../database');
    const db = database.getDb();

    const session = await new Promise((resolve, reject) => {
      const sql = `
        SELECT bs.*, b.* FROM bot_sessions bs
        JOIN bots b ON bs.bot_id = b.id
        WHERE bs.session_token = ? AND bs.is_active = 1 AND bs.expires_at > CURRENT_TIMESTAMP
      `;
      db.get(sql, [sessionToken], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!session) {
      return res.status(401).json({ error: 'Invalid or expired session token' });
    }

    req.session = {
      id: session.id,
      bot_id: session.bot_id,
      expires_at: session.expires_at
    };
    req.bot = new Bot({
      id: session.bot_id,
      name: session.name,
      token: session.token,
      description: session.description,
      owner_id: session.owner_id,
      is_active: session.is_active
    });

    next();
  } catch (error) {
    console.error('Session validation error:', error);
    res.status(500).json({ error: 'Session validation failed' });
  }
};

// Utility function để tạo JWT token
const generateAccessToken = (user) => {
  return jwt.sign(user, process.env.JWT_SECRET, { expiresIn: '24h' });
};

module.exports = {
  authenticateToken,
  authenticateBotToken,
  checkBotOwnership,
  validateSessionToken,
  generateAccessToken
};
