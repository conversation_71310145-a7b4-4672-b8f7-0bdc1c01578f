/**
 * Demo: <PERSON><PERSON><PERSON> sử dụng Bot Backend API
 * Giống như Discord/Telegram Bot
 */

const axios = require('axios');

const API_BASE = 'http://192.168.2.4:3001/api';

class BotClient {
  constructor(botToken) {
    this.botToken = botToken;
    this.apiClient = axios.create({
      baseURL: API_BASE,
      headers: {
        'X-Bot-Token': botToken,
        'Content-Type': 'application/json'
      }
    });
  }

  // Thực thi lệnh bot (giống Discord bot)
  async executeCommand(command, inputData = {}) {
    try {
      const response = await this.apiClient.post('/bots/execute', {
        command: command,
        input_data: inputData
      });
      return response.data;
    } catch (error) {
      console.error('Bot execution error:', error.response?.data || error.message);
      return { success: false, error: error.message };
    }
  }

  // L<PERSON>y thông tin bot
  async getBotInfo() {
    try {
      const response = await this.apiClient.get('/bots/info');
      return response.data;
    } catch (error) {
      console.error('Get bot info error:', error.response?.data || error.message);
      return null;
    }
  }
}

// Demo sử dụng
async function demoUsage() {
  console.log('=== BOT BACKEND DEMO ===\n');

  // 1. Đăng ký user và tạo bot
  console.log('1. Đăng ký user...');
  const userResponse = await axios.post(`${API_BASE}/auth/register`, {
    username: 'demo_user',
    password: 'password123',
    email: '<EMAIL>'
  });

  const userToken = userResponse.data.token;
  console.log('✓ User đã đăng ký thành công');

  // 2. Tạo bot
  console.log('\n2. Tạo bot...');
  const botResponse = await axios.post(`${API_BASE}/bots`, {
    name: 'Demo Bot',
    description: 'Bot demo giống Discord/Telegram'
  }, {
    headers: { 'Authorization': `Bearer ${userToken}` }
  });

  const bot = botResponse.data.bot;
  const botToken = bot.token;
  console.log('✓ Bot đã được tạo');
  console.log(`Bot Token: ${botToken}`);

  // 3. Nạp code vào bot (giống như lập trình Discord bot)
  console.log('\n3. Nạp code vào bot...');

  // Code cho Echo Bot
  const echoCode = `
    // Echo Bot - trả lại tin nhắn người dùng gửi
    const message = input.message || 'Hello!';
    console.log('Received message:', message);
    return 'Echo: ' + message;
  `;

  await axios.post(`${API_BASE}/bots/${bot.id}/code`, {
    code: echoCode,
    language: 'javascript',
    description: 'Echo Bot Command'
  }, {
    headers: { 'Authorization': `Bearer ${userToken}` }
  });

  // Code cho Calculator Bot
  const calculatorCode = `
    // Calculator Bot
    const { operation, a, b } = input;
    console.log('Calculator operation:', operation, a, b);

    switch(operation) {
      case 'add': return a + b;
      case 'subtract': return a - b;
      case 'multiply': return a * b;
      case 'divide':
        if (b === 0) return 'Error: Division by zero';
        return a / b;
      default:
        return 'Supported operations: add, subtract, multiply, divide';
    }
  `;

  await axios.post(`${API_BASE}/bots/${bot.id}/code`, {
    code: calculatorCode,
    language: 'javascript',
    description: 'Calculator Bot Command'
  }, {
    headers: { 'Authorization': `Bearer ${userToken}` }
  });

  console.log('✓ Code đã được nạp vào bot');

  // 4. Sử dụng bot từ bên ngoài (giống Discord/Telegram bot client)
  console.log('\n4. Sử dụng bot từ external client...');

  const botClient = new BotClient(botToken);

  // Test Echo Bot
  console.log('\n--- Test Echo Bot ---');
  const echoResult = await botClient.executeCommand('echo', {
    message: 'Hello from external client!'
  });
  console.log('Echo Result:', echoResult);

  // Test Calculator Bot
  console.log('\n--- Test Calculator Bot ---');
  const calcResult = await botClient.executeCommand('calculate', {
    operation: 'add',
    a: 10,
    b: 5
  });
  console.log('Calculator Result:', calcResult);

  // Lấy thông tin bot
  console.log('\n--- Bot Info ---');
  const botInfo = await botClient.getBotInfo();
  console.log('Bot Info:', botInfo);

  console.log('\n=== DEMO HOÀN THÀNH ===');
}

// Chạy demo nếu file được gọi trực tiếp
if (require.main === module) {
  demoUsage().catch(console.error);
}

module.exports = { BotClient, demoUsage };
