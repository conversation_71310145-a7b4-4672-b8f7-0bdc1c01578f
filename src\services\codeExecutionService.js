const ivm = require('isolated-vm');
const CodeInjection = require('../models/CodeInjection');

class CodeExecutionService {
  constructor() {
    this.maxExecutionTime = 5000; // 5 seconds
    this.maxCodeSize = parseInt(process.env.MAX_CODE_SIZE) || 10000;
  }

  async executeCode(codeInjection, inputData = {}) {
    const startTime = Date.now();
    let result = null;
    let error = null;

    try {
      // Validate code size
      if (codeInjection.code.length > this.maxCodeSize) {
        throw new Error(`Code size exceeds maximum limit of ${this.maxCodeSize} characters`);
      }

      // Execute code based on language
      switch (codeInjection.language.toLowerCase()) {
        case 'javascript':
        case 'js':
          result = await this.executeJavaScript(codeInjection.code, inputData);
          break;
        default:
          throw new Error(`Unsupported language: ${codeInjection.language}`);
      }

    } catch (err) {
      error = err.message;
      console.error('Code execution error:', err);
    }

    const executionTime = Date.now() - startTime;

    // Log execution
    try {
      await codeInjection.logExecution(inputData, result, error, executionTime);
      await codeInjection.incrementExecutionCount();
    } catch (logError) {
      console.error('Failed to log execution:', logError);
    }

    return {
      success: !error,
      result: result,
      error: error,
      execution_time_ms: executionTime,
      code_injection_id: codeInjection.id
    };
  }

  async executeJavaScript(code, inputData) {
    // Create isolated VM
    const isolate = new ivm.Isolate({ memoryLimit: 32 });
    const context = await isolate.createContext();

    // Set up global objects
    const jail = context.global;
    await jail.set('global', jail.derefInto());

    // Add input data
    await jail.set('input', new ivm.ExternalCopy(inputData).copyInto());

    // Add console.log functionality
    const logs = [];
    await jail.set('console', new ivm.ExternalCopy({
      log: (...args) => {
        logs.push(args.join(' '));
      }
    }).copyInto());

    // Add safe built-in objects
    await jail.set('Math', new ivm.ExternalCopy(Math).copyInto());
    await jail.set('Date', new ivm.ExternalCopy(Date).copyInto());
    await jail.set('JSON', new ivm.ExternalCopy(JSON).copyInto());

    // Wrap code in a function to capture return value
    const wrappedCode = `
      (function() {
        ${code}
      })();
    `;

    // Execute with timeout
    const result = await context.eval(wrappedCode, { timeout: this.maxExecutionTime });

    // Clean up
    isolate.dispose();

    return {
      result: result,
      logs: logs
    };
  }

  async executeBotCommand(botId, command, inputData = {}) {
    try {
      // Find active code injections for the bot
      const codeInjections = await CodeInjection.findActiveByBotId(botId);

      if (codeInjections.length === 0) {
        return {
          success: false,
          error: 'No active code injections found for this bot'
        };
      }

      // For now, execute the first active code injection
      // In a more advanced implementation, you could have command routing
      const codeInjection = codeInjections[0];

      // Add command and bot context to input data
      const enhancedInputData = {
        ...inputData,
        command: command,
        bot_id: botId,
        timestamp: new Date().toISOString()
      };

      return await this.executeCode(codeInjection, enhancedInputData);
    } catch (error) {
      console.error('Bot command execution error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  validateCode(code, language = 'javascript') {
    const errors = [];

    // Basic validation
    if (!code || typeof code !== 'string') {
      errors.push('Code must be a non-empty string');
    }

    if (code.length > this.maxCodeSize) {
      errors.push(`Code size exceeds maximum limit of ${this.maxCodeSize} characters`);
    }

    // Language-specific validation
    switch (language.toLowerCase()) {
      case 'javascript':
      case 'js':
        // Check for potentially dangerous patterns
        const dangerousPatterns = [
          /require\s*\(/,
          /import\s+/,
          /process\./,
          /global\./,
          /eval\s*\(/,
          /Function\s*\(/,
          /setTimeout\s*\(/,
          /setInterval\s*\(/
        ];

        for (const pattern of dangerousPatterns) {
          if (pattern.test(code)) {
            errors.push(`Potentially dangerous pattern detected: ${pattern.source}`);
          }
        }
        break;
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  async getExecutionStats(botId) {
    const database = require('../database');
    const db = database.getDb();

    return new Promise((resolve, reject) => {
      const sql = `
        SELECT
          COUNT(*) as total_executions,
          AVG(execution_time_ms) as avg_execution_time,
          MAX(execution_time_ms) as max_execution_time,
          MIN(execution_time_ms) as min_execution_time,
          COUNT(CASE WHEN error_message IS NOT NULL THEN 1 END) as error_count,
          COUNT(CASE WHEN error_message IS NULL THEN 1 END) as success_count
        FROM execution_logs
        WHERE bot_id = ?
      `;

      db.get(sql, [botId], (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }
}

module.exports = new CodeExecutionService();
