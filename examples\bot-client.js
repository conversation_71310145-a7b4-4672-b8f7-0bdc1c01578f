/**
 * Bot Client - Sử dụng token để điều khiển bot
 * Giống như Discord.js hoặc Telegram Bot
 */

const axios = require('axios');

class BotClient {
  constructor(botToken, apiBase = 'http://192.168.2.4:3001/api') {
    this.botToken = botToken;
    this.apiBase = apiBase;
    this.isReady = false;
    
    // Tạo axios instance với bot token
    this.api = axios.create({
      baseURL: apiBase,
      headers: {
        'X-Bot-Token': botToken,
        'Content-Type': 'application/json'
      }
    });
  }

  // Khởi động bot
  async start() {
    try {
      console.log('🤖 Starting bot...');
      
      // Kiểm tra bot token
      const botInfo = await this.getBotInfo();
      console.log(`✅ Bot connected: ${botInfo.bot.name}`);
      console.log(`📊 Total executions: ${botInfo.stats.total_executions || 0}`);
      
      this.isReady = true;
      this.onReady();
      
      return true;
    } catch (error) {
      console.error('❌ Failed to start bot:', error.response?.data || error.message);
      return false;
    }
  }

  // Event khi bot sẵn sàng
  onReady() {
    console.log('🚀 Bot is ready!');
  }

  // Lấy thông tin bot
  async getBotInfo() {
    const response = await this.api.get('/bots/info');
    return response.data;
  }

  // Thực thi lệnh bot
  async execute(command, inputData = {}) {
    try {
      const response = await this.api.post('/bots/execute', {
        command: command,
        input_data: inputData
      });
      
      return response.data;
    } catch (error) {
      console.error(`❌ Command '${command}' failed:`, error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.error || error.message
      };
    }
  }

  // Gửi tin nhắn (giống Discord bot)
  async sendMessage(message, channel = 'general') {
    return await this.execute('message', {
      content: message,
      channel: channel,
      timestamp: new Date().toISOString()
    });
  }

  // Xử lý lệnh với prefix (giống Discord bot)
  async handleCommand(message, prefix = '!') {
    if (!message.startsWith(prefix)) return null;
    
    const args = message.slice(prefix.length).trim().split(/ +/);
    const command = args.shift().toLowerCase();
    
    console.log(`🔧 Executing command: ${command} with args:`, args);
    
    switch (command) {
      case 'ping':
        return await this.execute('ping');
        
      case 'echo':
        return await this.execute('echo', { message: args.join(' ') });
        
      case 'calc':
      case 'calculate':
        if (args.length < 3) {
          return { error: 'Usage: !calc <operation> <a> <b>' };
        }
        return await this.execute('calculate', {
          operation: args[0],
          a: parseFloat(args[1]),
          b: parseFloat(args[2])
        });
        
      case 'random':
        const min = parseInt(args[0]) || 1;
        const max = parseInt(args[1]) || 100;
        const count = parseInt(args[2]) || 1;
        return await this.execute('random', { min, max, count });
        
      case 'dice':
      case 'roll':
        const sides = parseInt(args[0]) || 6;
        const diceCount = parseInt(args[1]) || 1;
        return await this.execute('dice', { sides, count: diceCount });
        
      case 'password':
      case 'pwd':
        const length = parseInt(args[0]) || 12;
        return await this.execute('password', { length });
        
      case 'help':
        return {
          success: true,
          result: {
            commands: [
              '!ping - Test bot response',
              '!echo <message> - Echo message back',
              '!calc <op> <a> <b> - Calculate (add, subtract, multiply, divide)',
              '!random [min] [max] [count] - Generate random numbers',
              '!dice [sides] [count] - Roll dice',
              '!password [length] - Generate password',
              '!help - Show this help'
            ]
          }
        };
        
      default:
        return await this.execute(command, { args });
    }
  }

  // Mô phỏng chat bot loop
  async startChatLoop() {
    console.log('\n💬 Chat mode started. Type commands or messages:');
    console.log('Available commands: !ping, !echo, !calc, !random, !dice, !password, !help');
    console.log('Type "exit" to quit\n');
    
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const askQuestion = () => {
      rl.question('You: ', async (input) => {
        if (input.toLowerCase() === 'exit') {
          console.log('👋 Goodbye!');
          rl.close();
          return;
        }

        let result;
        if (input.startsWith('!')) {
          // Xử lý command
          result = await this.handleCommand(input);
        } else {
          // Gửi tin nhắn thường
          result = await this.sendMessage(input);
        }

        if (result) {
          console.log('Bot:', JSON.stringify(result.result || result.error || result, null, 2));
        }
        
        askQuestion();
      });
    };

    askQuestion();
  }

  // Tự động thực thi lệnh theo interval (giống cron job)
  startAutoTasks() {
    console.log('⏰ Starting auto tasks...');
    
    // Ping bot mỗi 30 giây
    setInterval(async () => {
      const result = await this.execute('ping');
      if (result.success) {
        console.log('💓 Bot heartbeat OK');
      }
    }, 30000);

    // Tạo số random mỗi phút
    setInterval(async () => {
      const result = await this.execute('random', { min: 1, max: 1000 });
      if (result.success) {
        console.log('🎲 Random number:', result.result.numbers[0]);
      }
    }, 60000);
  }
}

// Ví dụ sử dụng
async function main() {
  // Thay YOUR_BOT_TOKEN bằng token thật của bạn
  const BOT_TOKEN = process.env.BOT_TOKEN || 'YOUR_BOT_TOKEN_HERE';
  
  if (BOT_TOKEN === 'YOUR_BOT_TOKEN_HERE') {
    console.log('❌ Please set BOT_TOKEN environment variable or edit the code');
    console.log('Example: BOT_TOKEN=your_actual_token node examples/bot-client.js');
    return;
  }

  // Tạo bot client
  const bot = new BotClient(BOT_TOKEN);

  // Khởi động bot
  const started = await bot.start();
  if (!started) {
    console.log('❌ Failed to start bot');
    return;
  }

  // Test một số lệnh
  console.log('\n🧪 Testing bot commands...\n');

  // Test ping
  const pingResult = await bot.execute('ping');
  console.log('Ping result:', pingResult);

  // Test echo
  const echoResult = await bot.execute('echo', { message: 'Hello from client!' });
  console.log('Echo result:', echoResult);

  // Test calculator
  const calcResult = await bot.execute('calculate', { operation: 'multiply', a: 7, b: 8 });
  console.log('Calc result:', calcResult);

  // Test command handling
  console.log('\n🔧 Testing command handling...\n');
  
  const commands = [
    '!ping',
    '!echo Hello World!',
    '!calc add 10 5',
    '!random 1 100 3',
    '!dice 20 2',
    '!password 16',
    '!help'
  ];

  for (const cmd of commands) {
    console.log(`\nCommand: ${cmd}`);
    const result = await bot.handleCommand(cmd);
    console.log('Result:', result?.result || result?.error || 'No result');
  }

  // Bắt đầu chat loop (uncomment để test interactive)
  // await bot.startChatLoop();

  // Bắt đầu auto tasks (uncomment để test auto tasks)
  // bot.startAutoTasks();
}

// Chạy nếu file được gọi trực tiếp
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { BotClient };
