---
Checks: 'bugprone-*,clang-*,hicpp-*,misc-*,modernize-*,performance-*,readability-*,-bugprone-easily-swappable-parameters,-hicpp-avoid-c-arrays,-hicpp-no-array-decay,-hicpp-no-malloc,-hicpp-signed-bitwise,-hicpp-use-override,-misc-const-correctness,-misc-no-recursion,-misc-non-private-member-variables-in-classes,-modernize-avoid-c-arrays,-modernize-use-nodiscard,-readability-else-after-return,-readability-magic-numbers,-readability-function-cognitive-complexity'
HeaderFilterRegex: 'src/*'
CheckOptions:
  - key:             readability-identifier-length.MinimumVariableNameLength
    value:           '2'
  - key:             readability-identifier-length.MinimumParameterNameLength
    value:           '2'
  - key:             hicpp-special-member-functions.AllowMissingMoveFunctions
    value:           '1'
  - key:             hicpp-special-member-functions.AllowSoleDefaultDtor
    value:           '1'
...
