{"name": "isolated-vm", "version": "5.0.4", "description": "Access to multiple isolates", "main": "isolated-vm.js", "types": "isolated-vm.d.ts", "engines": {"node": ">=18.0.0"}, "scripts": {"install": "prebuild-install || (node-gyp rebuild --release -j max && node-gyp clean)", "rebuild": "node-gyp rebuild --release -j max", "lint": "find src -name '*.cc' | xargs -n1 clang-tidy", "test": "node test.js"}, "dependencies": {"prebuild-install": "^7.1.2"}, "devDependencies": {"isolated-vm": ".", "prebuild": "^13.0.1"}, "binary": {"module_path": "out"}, "repository": {"type": "git", "url": "git+https://github.com/laverdet/isolated-vm.git"}, "author": "https://github.com/laverdet/", "license": "ISC", "gypfile": true, "bugs": {"url": "https://github.com/laverdet/isolated-vm/issues"}, "homepage": "https://github.com/laverdet/isolated-vm#readme"}