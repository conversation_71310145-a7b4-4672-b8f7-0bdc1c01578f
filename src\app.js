const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const database = require('./database');
const authRoutes = require('./routes/auth');
const botRoutes = require('./routes/bot');

class App {
  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());

    // CORS
    this.app.use(cors({
      origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
      credentials: true
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
      max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
      message: {
        error: 'Too many requests from this IP, please try again later.'
      }
    });
    this.app.use(limiter);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0'
      });
    });

    // API routes
    this.app.use('/api/auth', authRoutes);
    this.app.use('/api/bots', botRoutes);

    // API documentation
    this.app.get('/api', (req, res) => {
      res.json({
        message: 'Bot Backend API',
        version: '1.0.0',
        endpoints: {
          auth: {
            'POST /api/auth/register': 'Register a new user',
            'POST /api/auth/login': 'Login user',
            'GET /api/auth/me': 'Get current user info'
          },
          bots: {
            'POST /api/bots': 'Create a new bot',
            'GET /api/bots': 'Get all user bots',
            'GET /api/bots/:botId': 'Get specific bot',
            'PUT /api/bots/:botId': 'Update bot',
            'DELETE /api/bots/:botId': 'Delete bot',
            'POST /api/bots/:botId/session': 'Create bot session',
            'GET /api/bots/:botId/code': 'Get bot code injections',
            'POST /api/bots/:botId/code': 'Add code injection',
            'POST /api/bots/:botId/code/:injectionId/execute': 'Execute code injection',
            'POST /api/bots/execute': 'Execute bot command (bot token required)',
            'GET /api/bots/info': 'Get bot info (bot token required)'
          }
        },
        authentication: {
          user_endpoints: 'Use Bearer token in Authorization header',
          bot_endpoints: 'Use bot token in X-Bot-Token header or token query parameter'
        }
      });
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Endpoint not found',
        path: req.originalUrl,
        method: req.method
      });
    });
  }

  setupErrorHandling() {
    // Global error handler
    this.app.use((err, req, res, next) => {
      console.error('Unhandled error:', err);

      // Don't leak error details in production
      const isDevelopment = process.env.NODE_ENV === 'development';

      res.status(err.status || 500).json({
        error: err.message || 'Internal server error',
        ...(isDevelopment && { stack: err.stack })
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (err) => {
      console.error('Uncaught Exception:', err);
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });
  }

  async start(port = 3000, host = '0.0.0.0') {
    try {
      // Initialize database
      await database.connect();

      // Start server
      this.server = this.app.listen(port, host, () => {
        console.log(`Server running on ${host}:${port}`);
        console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
        console.log(`Local: http://localhost:${port}/api`);
        console.log(`Network: http://${this.getLocalIP()}:${port}/api`);
      });

      return this.server;
    } catch (error) {
      console.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  getLocalIP() {
    const { networkInterfaces } = require('os');
    const nets = networkInterfaces();

    for (const name of Object.keys(nets)) {
      for (const net of nets[name]) {
        // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
        if (net.family === 'IPv4' && !net.internal) {
          return net.address;
        }
      }
    }

    return 'localhost';
  }

  async stop() {
    if (this.server) {
      await new Promise((resolve) => {
        this.server.close(resolve);
      });
    }
    await database.close();
    console.log('Server stopped');
  }

  getApp() {
    return this.app;
  }
}

module.exports = App;
